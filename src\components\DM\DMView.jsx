import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { FaComments } from 'react-icons/fa';
import RealTimeDMSidebar from './RealTimeDMSidebar';
import RealTimeDMChatRoom from './RealTimeDMChatRoom';
import ZentroBotChat from './ZentroBotChat';
import DataManager from '../Debug/DataManager';
import ZentroSidebar from '../../componenents/UI/ZentroSidebar';
import { getTheme, applyTheme, defaultTheme } from '../../styles/themes';
import { useUser } from '../../contexts/UserContext';
import { useTheme } from '../../contexts/ThemeContext';

const DMView = () => {
  const location = useLocation();
  const { canAccessDataManager } = useUser();
  const { currentTheme } = useTheme();
  const [selectedChat, setSelectedChat] = useState(null);
  const [currentView, setCurrentView] = useState('dm');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 1024);
  const [showDataManager, setShowDataManager] = useState(false);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Check for selectedChat from navigation state
  useEffect(() => {
    if (location.state?.selectedChat) {
      console.log('DMView: Setting selected chat from navigation:', location.state.selectedChat);
      setSelectedChat(location.state.selectedChat);
    }
  }, [location.state]);

  const handleSelectChat = (chat) => {
    setSelectedChat(chat);
  };

  const handleBackToSidebar = () => {
    setSelectedChat(null);
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  // Check if this is Zentro Bot chat
  const isZentroBotChat = selectedChat?.otherUser?.id === 'zentro_bot' ||
                         selectedChat?.otherUser?.isBot ||
                         selectedChat?.id === 'zentro_bot_chat' ||
                         selectedChat?.name === 'Zenny' ||
                         selectedChat?.otherUser?.name === 'Zenny';

  // Mobile view - show only one panel at a time
  if (isMobile) {
    if (selectedChat) {
      return (
        <div
          className="h-screen flex"
          style={{ backgroundColor: currentTheme.colors.background }}
        >
          <ZentroSidebar currentView={currentView} setCurrentView={handleViewChange} />
          <div className="flex-1">
            {isZentroBotChat ? (
              <ZentroBotChat
                theme={currentTheme}
                onBack={handleBackToSidebar}
              />
            ) : (
              <RealTimeDMChatRoom
                chatUser={selectedChat.otherUser || selectedChat}
                chatId={selectedChat.id}
                onBack={handleBackToSidebar}
                theme={currentTheme}
              />
            )}
          </div>
        </div>
      );
    }

    return (
      <div
        className="h-screen flex"
        style={{ backgroundColor: currentTheme.colors.background }}
      >
        <ZentroSidebar currentView={currentView} setCurrentView={handleViewChange} />
        <div className="flex-1">
          <RealTimeDMSidebar
            onSelectChat={handleSelectChat}
            selectedChat={selectedChat}
            currentTheme={currentTheme}
            isMinimal={false}
          />
        </div>
      </div>
    );
  }

  // Desktop view - show multiple panels
  return (
    <div
      className="h-screen flex"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      {/* Sidebar */}
      <ZentroSidebar currentView={currentView} setCurrentView={handleViewChange} />

      {/* DM List - Minimal when chat is selected */}
      <div
        className={`${selectedChat ? 'w-16' : 'w-80'} border-r transition-all duration-300`}
        style={{ borderColor: currentTheme.colors.borderMuted }}
      >
        <RealTimeDMSidebar
          onSelectChat={handleSelectChat}
          selectedChat={selectedChat}
          currentTheme={currentTheme}
          isMinimal={selectedChat !== null}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Chat Area */}
        <div className="w-full">
          {selectedChat ? (
            isZentroBotChat ? (
              <ZentroBotChat
                theme={currentTheme}
                onBack={handleBackToSidebar}
              />
            ) : (
              <RealTimeDMChatRoom
                chatUser={selectedChat.otherUser || selectedChat}
                chatId={selectedChat.id}
                onBack={handleBackToSidebar}
                theme={currentTheme}
              />
            )
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FaComments
                  className="text-6xl mx-auto mb-4"
                  style={{ color: currentTheme.colors.textMuted }}
                />
                <h3
                  className="text-2xl font-semibold mb-2"
                  style={{ color: currentTheme.colors.text }}
                >
                  Welcome to Zentro Messages
                </h3>
                <p
                  className="mb-6 max-w-md"
                  style={{ color: currentTheme.colors.textMuted }}
                >
                  Select a conversation from the sidebar to start chatting with friends, share media, and enjoy our beautiful themes.
                </p>
                <div className="space-y-3 text-sm" style={{ color: currentTheme.colors.textSecondary }}>
                  <div className="flex items-center justify-center space-x-2">
                    <span>🔄</span>
                    <span>Real-time messaging</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span>📸</span>
                    <span>Share photos and videos</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span>🎨</span>
                    <span>Multiple beautiful themes</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span>🤖</span>
                    <span>AI assistant (Zentro Bot)</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span>😊</span>
                    <span>Reactions and replies</span>
                  </div>
                </div>

                {/* Debug Data Manager Button - Admin Only */}
                {canAccessDataManager() && (
                  <div className="mt-6">
                    <button
                      onClick={() => setShowDataManager(true)}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors text-sm"
                    >
                      🗑️ Data Manager (Admin)
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Data Manager Modal */}
      {showDataManager && (
        <DataManager onClose={() => setShowDataManager(false)} />
      )}
    </div>
  );
};

export default DMView;
